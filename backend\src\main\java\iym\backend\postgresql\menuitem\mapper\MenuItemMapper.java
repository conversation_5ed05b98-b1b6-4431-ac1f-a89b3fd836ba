package iym.backend.postgresql.menuitem.mapper;

import org.mapstruct.*;
import iym.backend.postgresql.menuitem.dto.MenuItemDto;
import iym.backend.postgresql.menuitem.entity.MenuItem;
import iym.backend.postgresql.shared.mapper.BaseMapper;

@Mapper(componentModel = "spring")
public interface MenuItemMapper extends BaseMapper<MenuItem, MenuItemDto> {
    @Mapping(target = "yetkiIds", ignore = true)
    @Mapping(target = "yetkiAdlari", ignore = true)
    @Mapping(target = "parentId", ignore = true)
    MenuItemDto toDto(MenuItem menuItem);

    @InheritInverseConfiguration
    MenuItem toEntity(MenuItemDto dto);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void updateEntityFromDto(MenuItemDto dto, @MappingTarget MenuItem entity);

}
