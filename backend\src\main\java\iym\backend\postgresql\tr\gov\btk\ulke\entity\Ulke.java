package iym.backend.postgresql.tr.gov.btk.ulke.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import iym.backend.postgresql.tr.gov.btk.shared.entity.BaseEntity;

@Entity
@Table(name = "Ulkeler")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@SQLDelete(sql = "UPDATE Ulkeler SET is_deleted = true, deleted_at = now() WHERE id = ?")
@Where(clause = "is_deleted = false")
public class Ulke extends BaseEntity {
    private String name;
    private String code;
}
