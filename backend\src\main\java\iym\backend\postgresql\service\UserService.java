package iym.backend.postgresql.service;

import iym.backend.postgresql.dto.UserDto;
import iym.backend.postgresql.entity.User;
import iym.backend.postgresql.mapper.UserMapper;
import iym.backend.postgresql.repository.UserRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Service for User operations using PostgreSQL database
 */
@Service
@Slf4j
@Transactional("postgresqlTransactionManager")
public class UserService extends BaseServiceImpl<User, UserDto, Long> {

    private final UserRepository userRepository;

    public UserService(UserRepository userRepository, UserMapper userMapper) {
        super(userRepository, userMapper);
        this.userRepository = userRepository;
    }

    @Transactional(value = "postgresqlTransactionManager", readOnly = true)
    public Optional<UserDto> findByUsername(String username) {
        log.debug("Finding user by username: {}", username);
        return userRepository.findByUsername(username)
                .map(mapper::toDto);
    }

    @Transactional(value = "postgresqlTransactionManager", readOnly = true)
    public Optional<UserDto> findByEmail(String email) {
        log.debug("Finding user by email: {}", email);
        return userRepository.findByEmail(email)
                .map(mapper::toDto);
    }

    @Transactional(value = "postgresqlTransactionManager", readOnly = true)
    public boolean existsByUsername(String username) {
        log.debug("Checking if username exists: {}", username);
        return userRepository.existsByUsername(username);
    }

    @Transactional(value = "postgresqlTransactionManager", readOnly = true)
    public boolean existsByEmail(String email) {
        log.debug("Checking if email exists: {}", email);
        return userRepository.existsByEmail(email);
    }

    @Override
    @Transactional("postgresqlTransactionManager")
    public UserDto save(UserDto userDto) {
        log.info("Creating new user: {}", userDto.getUsername());
        
        // Check if username already exists
        if (existsByUsername(userDto.getUsername())) {
            throw new RuntimeException("Username already exists: " + userDto.getUsername());
        }
        
        // Check if email already exists
        if (existsByEmail(userDto.getEmail())) {
            throw new RuntimeException("Email already exists: " + userDto.getEmail());
        }
        
        return super.save(userDto);
    }
}
