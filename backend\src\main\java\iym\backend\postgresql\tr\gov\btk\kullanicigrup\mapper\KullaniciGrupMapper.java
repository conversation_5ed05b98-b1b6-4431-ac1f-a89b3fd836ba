package iym.backend.postgresql.tr.gov.btk.kullanicigrup.mapper;

import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import iym.backend.postgresql.tr.gov.btk.kullanicigrup.dto.KullaniciGrupDto;
import iym.backend.postgresql.tr.gov.btk.kullanicigrup.entity.KullaniciGrup;
import iym.backend.postgresql.tr.gov.btk.shared.mapper.BaseMapper;

import java.util.List;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public interface KullaniciGrupMapper extends BaseMapper<KullaniciGrup, KullaniciGrupDto> {

    @AfterMapping
    default void fillYetkiIdList(KullaniciGrup entity, @MappingTarget KullaniciGrupDto dto) {
        if (entity.getKullaniciGrupYetkiler() != null) {
            List<Long> yetkiIdList = entity.getKullaniciGrupYetkiler().stream()
                    .map(kg -> kg.getYetki().getId())
                    .collect(Collectors.toList());
            dto.setYetkiIdList(yetkiIdList);
        }
    }
}
