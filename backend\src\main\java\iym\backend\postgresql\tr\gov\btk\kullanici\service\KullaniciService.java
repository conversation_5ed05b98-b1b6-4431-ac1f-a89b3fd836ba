package iym.backend.postgresql.tr.gov.btk.kullanici.service;

import iym.backend.postgresql.tr.gov.btk.authentication.dto.ChangePasswordRequest;
import iym.backend.postgresql.tr.gov.btk.authentication.dto.JwtResponse;
import iym.backend.postgresql.tr.gov.btk.kullanici.dto.KullaniciDto;
import iym.backend.postgresql.tr.gov.btk.shared.service.BaseService;

public interface KullaniciService extends BaseService<KullaniciDto, Long> {
    JwtResponse changePassword(ChangePasswordRequest request);
}
