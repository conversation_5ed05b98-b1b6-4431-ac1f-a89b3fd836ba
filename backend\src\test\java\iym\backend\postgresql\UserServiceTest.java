package iym.backend.postgresql;

import iym.backend.postgresql.dto.UserDto;
import iym.backend.postgresql.entity.User;
import iym.backend.postgresql.service.UserService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration test for UserService using PostgreSQL (H2 in-memory for testing)
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional("postgresqlTransactionManager")
class UserServiceTest {

    @Autowired
    private UserService userService;

    @Test
    void testCreateAndFindUser() {
        // Given
        UserDto userDto = UserDto.builder()
                .username("testuser")
                .email("<EMAIL>")
                .firstName("Test")
                .lastName("User")
                .status(User.UserStatus.ACTIVE)
                .build();

        // When
        UserDto savedUser = userService.save(userDto);

        // Then
        assertNotNull(savedUser);
        assertNotNull(savedUser.getId());
        assertEquals("testuser", savedUser.getUsername());
        assertEquals("<EMAIL>", savedUser.getEmail());
        assertEquals("Test", savedUser.getFirstName());
        assertEquals("User", savedUser.getLastName());
        assertEquals(User.UserStatus.ACTIVE, savedUser.getStatus());
    }

    @Test
    void testFindByUsername() {
        // Given
        UserDto userDto = UserDto.builder()
                .username("finduser")
                .email("<EMAIL>")
                .firstName("Find")
                .lastName("User")
                .status(User.UserStatus.ACTIVE)
                .build();

        userService.save(userDto);

        // When
        var foundUser = userService.findByUsername("finduser");

        // Then
        assertTrue(foundUser.isPresent());
        assertEquals("finduser", foundUser.get().getUsername());
        assertEquals("<EMAIL>", foundUser.get().getEmail());
    }

    @Test
    void testExistsByUsername() {
        // Given
        UserDto userDto = UserDto.builder()
                .username("existsuser")
                .email("<EMAIL>")
                .firstName("Exists")
                .lastName("User")
                .status(User.UserStatus.ACTIVE)
                .build();

        userService.save(userDto);

        // When & Then
        assertTrue(userService.existsByUsername("existsuser"));
        assertFalse(userService.existsByUsername("nonexistent"));
    }

    @Test
    void testDuplicateUsernameThrowsException() {
        // Given
        UserDto userDto1 = UserDto.builder()
                .username("duplicate")
                .email("<EMAIL>")
                .firstName("First")
                .lastName("User")
                .status(User.UserStatus.ACTIVE)
                .build();

        UserDto userDto2 = UserDto.builder()
                .username("duplicate")
                .email("<EMAIL>")
                .firstName("Second")
                .lastName("User")
                .status(User.UserStatus.ACTIVE)
                .build();

        userService.save(userDto1);

        // When & Then
        assertThrows(RuntimeException.class, () -> userService.save(userDto2));
    }
}
