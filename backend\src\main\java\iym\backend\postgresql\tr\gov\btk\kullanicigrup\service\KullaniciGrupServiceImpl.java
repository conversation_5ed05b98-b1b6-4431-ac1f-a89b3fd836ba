package iym.backend.postgresql.tr.gov.btk.kullanicigrup.service;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import iym.backend.postgresql.tr.gov.btk.kullanicigrup.dto.KullaniciGrupDto;
import iym.backend.postgresql.tr.gov.btk.kullanicigrup.entity.KullaniciGrup;
import iym.backend.postgresql.tr.gov.btk.kullanicigrup.mapper.KullaniciGrupMapper;
import iym.backend.postgresql.tr.gov.btk.kullanicigrup.repository.KullaniciGrupRepository;
import iym.backend.postgresql.tr.gov.btk.kullanicigrupyetki.entity.KullaniciGrupYetki;
import iym.backend.postgresql.tr.gov.btk.shared.mapper.BaseMapper;
import iym.backend.postgresql.tr.gov.btk.shared.repository.BaseRepository;
import iym.backend.postgresql.tr.gov.btk.shared.service.BaseServiceImpl;
import iym.backend.postgresql.tr.gov.btk.yetki.repository.YetkiRepository;

@Service

public class KullaniciGrupServiceImpl extends BaseServiceImpl<KullaniciGrup, KullaniciGrupDto, Long>
        implements KullaniciGrupService {

    private final KullaniciGrupRepository repository;
    private final KullaniciGrupMapper mapper;
    private final YetkiRepository yetkiRepository;

    public KullaniciGrupServiceImpl(KullaniciGrupRepository repository, @Qualifier("kullaniciGrupMapperImpl") KullaniciGrupMapper mapper, YetkiRepository yetkiRepository) {
        super(repository, mapper);
        this.repository = repository;
        this.mapper = mapper;
        this.yetkiRepository = yetkiRepository;
    }

    @Override
    public KullaniciGrupDto save(KullaniciGrupDto dto) {
        KullaniciGrup grup = new KullaniciGrup();
        grup.setId(dto.getId());
        grup.setAd(dto.getAd());

        var yetkiler = dto.getYetkiIdList().stream().map(id -> {
            var kgy = new KullaniciGrupYetki();
            kgy.setKullaniciGrup(grup);
            kgy.setYetki(yetkiRepository.findById(id).orElseThrow());
            return kgy;
        }).toList();

        grup.setKullaniciGrupYetkiler(yetkiler);
        return mapper.toDto(repository.save(grup));
    }
}


