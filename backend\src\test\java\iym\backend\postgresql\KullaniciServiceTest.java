package iym.backend.postgresql;

import iym.backend.postgresql.tr.gov.btk.kullanici.dto.KullaniciDto;
import iym.backend.postgresql.tr.gov.btk.kullanici.entity.Kullanici;
import iym.backend.postgresql.tr.gov.btk.kullanici.enums.enumKullaniciStatus;
import iym.backend.postgresql.tr.gov.btk.kullanici.service.KullaniciService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration test for KullaniciService using PostgreSQL (H2 in-memory for testing)
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional("postgresqlTransactionManager")
class KullaniciServiceTest {

    @Autowired
    private KullaniciService kullaniciService;

    @Test
    void testCreateAndFindKullanici() {
        // Given
        KullaniciDto kullaniciDto = KullaniciDto.builder()
                .kullaniciAdi("testuser")
                .email("<EMAIL>")
                .ad("Test")
                .soyad("User")
                .tcno("12345678901")
                .parola("password123")
                .status(enumKullaniciStatus.AKTIF)
                .build();

        // When
        KullaniciDto savedKullanici = kullaniciService.save(kullaniciDto);

        // Then
        assertNotNull(savedKullanici);
        assertNotNull(savedKullanici.getId());
        assertEquals("testuser", savedKullanici.getKullaniciAdi());
        assertEquals("<EMAIL>", savedKullanici.getEmail());
        assertEquals("Test", savedKullanici.getAd());
        assertEquals("User", savedKullanici.getSoyad());
        assertEquals(enumKullaniciStatus.AKTIF, savedKullanici.getStatus());
    }

    @Test
    void testFindByKullaniciAdi() {
        // Given
        KullaniciDto kullaniciDto = KullaniciDto.builder()
                .kullaniciAdi("finduser")
                .email("<EMAIL>")
                .ad("Find")
                .soyad("User")
                .tcno("12345678902")
                .parola("password123")
                .status(enumKullaniciStatus.AKTIF)
                .build();

        kullaniciService.save(kullaniciDto);

        // When
        var foundKullanici = kullaniciService.findByKullaniciAdi("finduser");

        // Then
        assertTrue(foundKullanici.isPresent());
        assertEquals("finduser", foundKullanici.get().getKullaniciAdi());
        assertEquals("<EMAIL>", foundKullanici.get().getEmail());
    }

    @Test
    void testFindAll() {
        // Given
        KullaniciDto kullanici1 = KullaniciDto.builder()
                .kullaniciAdi("user1")
                .email("<EMAIL>")
                .ad("User")
                .soyad("One")
                .tcno("12345678903")
                .parola("password123")
                .status(enumKullaniciStatus.AKTIF)
                .build();

        KullaniciDto kullanici2 = KullaniciDto.builder()
                .kullaniciAdi("user2")
                .email("<EMAIL>")
                .ad("User")
                .soyad("Two")
                .tcno("12345678904")
                .parola("password123")
                .status(enumKullaniciStatus.AKTIF)
                .build();

        kullaniciService.save(kullanici1);
        kullaniciService.save(kullanici2);

        // When
        var allKullanicilar = kullaniciService.findAll();

        // Then
        assertNotNull(allKullanicilar);
        assertTrue(allKullanicilar.size() >= 2);
    }
}
