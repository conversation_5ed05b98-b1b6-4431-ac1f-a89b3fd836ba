package iym.backend.postgresql.tr.gov.btk.shared.listener;

import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import iym.backend.postgresql.tr.gov.btk.shared.entity.BaseEntity;

import java.time.LocalDateTime;

public class AuditingEntityListener {

    @PrePersist
    public void setCreatedAt(BaseEntity entity) {
        entity.setCreatedAt(LocalDateTime.now());
        entity.setUpdatedAt(LocalDateTime.now());

        // <PERSON> o<PERSON> setle - Spring Security ile entegre edilecekse burada "kim" sorusunu yanıtlayabiliriz
        entity.setCreatedBy("system");
        entity.setUpdatedBy("system");
    }

    @PreUpdate
    public void setUpdatedAt(BaseEntity entity) {
        entity.setUpdatedAt(LocalDateTime.now());
        entity.setUpdatedBy("system");
    }
}
