package iym.backend.postgresql.tr.gov.btk.kullanici.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.*;


import iym.backend.postgresql.tr.gov.btk.kullanici.enums.enumKullaniciStatus;
import iym.backend.postgresql.tr.gov.btk.kullanicikullanicigrup.entity.KullaniciKullaniciGrup;
import iym.backend.postgresql.tr.gov.btk.shared.entity.BaseEntity;

import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "kullanicilar")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Kullanici extends BaseEntity {

    @Column(nullable = false)
    private String kullaniciAdi;

    @Column(length = 11)
    private String tcno;

    private String ad;

    private String soyad;

    private String email;

    @JsonIgnore
    @Column(nullable = false)
    private String parola;

    private String avatarPath;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private enumKullaniciStatus status = enumKullaniciStatus.SIFRE_DEGISTIRMELI;

    @OneToMany(mappedBy = "kullanici", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<KullaniciKullaniciGrup> kullaniciKullaniciGruplar = new ArrayList<>();

}
