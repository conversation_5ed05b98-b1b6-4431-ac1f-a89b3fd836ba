# Test Environment Configuration for Backend
# Multi-Database Configuration for Testing

# Oracle Database configuration (Primary) - for existing tests
spring.datasource.oracle.url=************************************
spring.datasource.oracle.driver-class-name=oracle.jdbc.OracleDriver
spring.datasource.oracle.username=iym
spring.datasource.oracle.password=iym

# PostgreSQL Database configuration (Secondary) - H2 in-memory for testing
spring.datasource.postgresql.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.postgresql.driver-class-name=org.h2.Driver
spring.datasource.postgresql.username=sa
spring.datasource.postgresql.password=

# JPA configuration for testing
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Logging configuration for tests
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
logging.level.iym.backend.postgresql=DEBUG
