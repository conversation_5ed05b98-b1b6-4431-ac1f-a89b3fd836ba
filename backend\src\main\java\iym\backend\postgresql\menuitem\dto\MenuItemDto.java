package iym.backend.postgresql.menuitem.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MenuItemDto {
    private Long id;
    private String label;
    private String icon;
    private String routerLink;
    private String queryParams;
    private Long parentId;
    private int menuOrder;

    private List<Long> yetkiIds;
    private List<MenuItemDto> items;
    private List<String> yetkiAdlari;
}
