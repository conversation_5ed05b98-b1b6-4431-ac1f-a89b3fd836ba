package iym.backend.postgresql;

import com.fasterxml.jackson.databind.ObjectMapper;
import iym.backend.postgresql.dto.UserDto;
import iym.backend.postgresql.entity.User;
import iym.backend.postgresql.service.UserService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration test for UserController
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional("postgresqlTransactionManager")
class UserControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private UserService userService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testCreateUser() throws Exception {
        // Given
        UserDto userDto = UserDto.builder()
                .username("testuser")
                .email("<EMAIL>")
                .firstName("Test")
                .lastName("User")
                .status(User.UserStatus.ACTIVE)
                .build();

        // When & Then
        mockMvc.perform(post("/api/postgresql/users")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(userDto)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.username").value("testuser"))
                .andExpect(jsonPath("$.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.firstName").value("Test"))
                .andExpect(jsonPath("$.lastName").value("User"));
    }

    @Test
    void testGetAllUsers() throws Exception {
        // Given
        UserDto userDto = UserDto.builder()
                .username("listuser")
                .email("<EMAIL>")
                .firstName("List")
                .lastName("User")
                .status(User.UserStatus.ACTIVE)
                .build();

        userService.save(userDto);

        // When & Then
        mockMvc.perform(get("/api/postgresql/users"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].username").value("listuser"));
    }

    @Test
    void testGetUserByUsername() throws Exception {
        // Given
        UserDto userDto = UserDto.builder()
                .username("getuser")
                .email("<EMAIL>")
                .firstName("Get")
                .lastName("User")
                .status(User.UserStatus.ACTIVE)
                .build();

        userService.save(userDto);

        // When & Then
        mockMvc.perform(get("/api/postgresql/users/username/getuser"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.username").value("getuser"))
                .andExpect(jsonPath("$.email").value("<EMAIL>"));
    }

    @Test
    void testGetUserByUsernameNotFound() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/postgresql/users/username/nonexistent"))
                .andExpect(status().isNotFound());
    }

    @Test
    void testCheckUsernameExists() throws Exception {
        // Given
        UserDto userDto = UserDto.builder()
                .username("checkuser")
                .email("<EMAIL>")
                .firstName("Check")
                .lastName("User")
                .status(User.UserStatus.ACTIVE)
                .build();

        userService.save(userDto);

        // When & Then
        mockMvc.perform(get("/api/postgresql/users/exists/username/checkuser"))
                .andExpect(status().isOk())
                .andExpect(content().string("true"));

        mockMvc.perform(get("/api/postgresql/users/exists/username/nonexistent"))
                .andExpect(status().isOk())
                .andExpect(content().string("false"));
    }
}
