package iym.backend.postgresql.tr.gov.btk.ulke.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import iym.backend.postgresql.tr.gov.btk.shared.service.BaseService;
import iym.backend.postgresql.tr.gov.btk.ulke.dto.UlkeDto;
import iym.backend.postgresql.tr.gov.btk.yetki.dto.YetkiDto;

public interface UlkeService  extends BaseService<UlkeDto, Long> {
    Page<UlkeDto> getPaged(Pageable pageable);
}
